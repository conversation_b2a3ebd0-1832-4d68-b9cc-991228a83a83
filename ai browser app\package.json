{"name": "ai-platforms-dashboard", "version": "1.0.0", "description": "تطبيق سطح مكتب لمقارنة منصات الذكاء الاصطناعي", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder", "dist": "electron-builder --publish=never", "pack": "electron-builder --dir"}, "keywords": ["AI", "platforms", "comparison", "desktop", "electron"], "author": "AI Platforms Team", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "build": {"appId": "com.aiplatforms.dashboard", "productName": "منصات الذكاء الاصطناعي", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules", "!dist"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}