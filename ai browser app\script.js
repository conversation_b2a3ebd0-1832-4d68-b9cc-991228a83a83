// ملف JavaScript الرئيسي لتطبيق منصات الذكاء الاصطناعي

// متغيرات النظام
let isComparisonMode = true; // تفعيل وضع المقارنة افتراضياً
let activePanel = 'left';
let leftPlatform = { url: '', name: '', icon: '' };
let rightPlatform = { url: '', name: '', icon: '' };

// عناصر DOM
const platformItems = document.querySelectorAll('.platform-item');
const comparisonToggle = document.getElementById('comparisonToggle');
const contentArea = document.getElementById('contentArea');
const leftPanel = document.getElementById('leftPanel');
const rightPanel = document.getElementById('rightPanel');

// عناصر القسم الأيسر
const leftFrame = document.getElementById('leftFrame');
const leftWelcomeScreen = document.getElementById('leftWelcomeScreen');
const leftLoadingIndicator = document.getElementById('leftLoadingIndicator');
const leftPlatformEl = document.getElementById('leftPlatform');
const leftIconEl = document.getElementById('leftIcon');

// عناصر القسم الأيمن
const rightFrame = document.getElementById('rightFrame');
const rightWelcomeScreen = document.getElementById('rightWelcomeScreen');
const rightLoadingIndicator = document.getElementById('rightLoadingIndicator');
const rightPlatformEl = document.getElementById('rightPlatform');
const rightIconEl = document.getElementById('rightIcon');

// تفعيل/إلغاء وضع المقارنة
comparisonToggle.addEventListener('click', function() {
    isComparisonMode = !isComparisonMode;
    this.classList.toggle('active', isComparisonMode);
    contentArea.classList.toggle('single-view', !isComparisonMode);
    
    if (isComparisonMode) {
        leftPanel.classList.add('active');
        rightPanel.classList.remove('active');
        activePanel = 'left';
    } else {
        leftPanel.classList.add('active');
        rightPanel.classList.remove('active');
    }
});

// إضافة مستمعي الأحداث للمنصات
platformItems.forEach(item => {
    item.addEventListener('click', function(event) {
        const platform = this.dataset.platform;
        const url = this.dataset.url;
        const name = this.querySelector('.platform-name').textContent;
        const iconEl = this.querySelector('.platform-icon');
        const icon = iconEl.textContent;
        
        // تحديد القسم المستهدف
        let targetPanel = activePanel;
        if (isComparisonMode && event.ctrlKey) {
            targetPanel = activePanel === 'left' ? 'right' : 'left';
        }
        
        if (platform === 'welcome') {
            showWelcomeScreen(targetPanel);
        } else {
            loadPlatform(url, name, icon, targetPanel);
        }
        
        // تحديث الحالة النشطة
        platformItems.forEach(i => i.classList.remove('active'));
        this.classList.add('active');
        
        // تحديث القسم النشط
        if (isComparisonMode) {
            leftPanel.classList.toggle('active', targetPanel === 'left');
            rightPanel.classList.toggle('active', targetPanel === 'right');
            activePanel = targetPanel;
        }
    });
});

function loadPlatform(url, name, icon, panel = 'left') {
    const platformData = { url, name, icon };
    
    if (panel === 'left') {
        leftPlatform = platformData;
        loadPlatformInPanel(url, name, icon, 'left');
    } else {
        rightPlatform = platformData;
        loadPlatformInPanel(url, name, icon, 'right');
    }
}

function loadPlatformInPanel(url, name, icon, panel) {
    const frame = panel === 'left' ? leftFrame : rightFrame;
    const welcomeScreen = panel === 'left' ? leftWelcomeScreen : rightWelcomeScreen;
    const loadingIndicator = panel === 'left' ? leftLoadingIndicator : rightLoadingIndicator;
    const platformEl = panel === 'left' ? leftPlatformEl : rightPlatformEl;
    const iconEl = panel === 'left' ? leftIconEl : rightIconEl;
    
    // إخفاء الشاشة الترحيبية وإظهار مؤشر التحميل
    welcomeScreen.style.display = 'none';
    loadingIndicator.style.display = 'block';
    frame.style.display = 'none';
    
    // تحديث عنوان المنصة والأيقونة
    platformEl.textContent = name;
    iconEl.textContent = icon;
    
    // إضافة معالج للأخطاء
    frame.onerror = function() {
        loadingIndicator.style.display = 'none';
        showError('لا يمكن تحميل هذه المنصة في الإطار. استخدم زر "فتح خارجياً" بدلاً من ذلك.', panel);
    };
    
    // تحميل المنصة
    frame.src = url;
    
    // إظهار الإطار بعد التحميل
    frame.onload = function() {
        try {
            // محاولة الوصول للمحتوى للتأكد من التحميل الناجح
            if (frame.contentDocument || frame.contentWindow) {
                loadingIndicator.style.display = 'none';
                frame.style.display = 'block';
            } else {
                throw new Error('Access denied');
            }
        } catch (error) {
            console.log('Frame access error:', error.message);
            loadingIndicator.style.display = 'none';
            showError('هذه المنصة لا تدعم التحميل المدمج. استخدم زر "فتح خارجياً".', panel);
        }
    };
    
    // في حالة فشل التحميل أو طول مدة التحميل
    setTimeout(() => {
        if (loadingIndicator.style.display !== 'none') {
            loadingIndicator.style.display = 'none';
            showError('انتهت مهلة التحميل. جرب فتح المنصة في تبويب منفصل.', panel);
        }
    }, 8000);
}

function showWelcomeScreen(panel = 'left') {
    const frame = panel === 'left' ? leftFrame : rightFrame;
    const welcomeScreen = panel === 'left' ? leftWelcomeScreen : rightWelcomeScreen;
    const loadingIndicator = panel === 'left' ? leftLoadingIndicator : rightLoadingIndicator;
    const platformEl = panel === 'left' ? leftPlatformEl : rightPlatformEl;
    const iconEl = panel === 'left' ? leftIconEl : rightIconEl;
    
    frame.style.display = 'none';
    loadingIndicator.style.display = 'none';
    welcomeScreen.style.display = 'flex';
    platformEl.textContent = 'اختر منصة';
    iconEl.textContent = '🏠';
    
    if (panel === 'left') {
        leftPlatform = { url: '', name: '', icon: '' };
    } else {
        rightPlatform = { url: '', name: '', icon: '' };
    }
}

function refreshPanel(panel) {
    const platformData = panel === 'left' ? leftPlatform : rightPlatform;
    if (platformData.url) {
        loadPlatformInPanel(platformData.url, platformData.name, platformData.icon, panel);
    }
}

function openPanelInNewTab(panel) {
    const platformData = panel === 'left' ? leftPlatform : rightPlatform;
    if (platformData.url) {
        window.open(platformData.url, '_blank');
    }
}

function swapPanels() {
    if (isComparisonMode && leftPlatform.url && rightPlatform.url) {
        const temp = leftPlatform;
        leftPlatform = rightPlatform;
        rightPlatform = temp;
        
        loadPlatformInPanel(leftPlatform.url, leftPlatform.name, leftPlatform.icon, 'left');
        loadPlatformInPanel(rightPlatform.url, rightPlatform.name, rightPlatform.icon, 'right');
    }
}

function openAllInNewTabs() {
    if (leftPlatform.url) {
        window.open(leftPlatform.url, '_blank');
    }
    if (rightPlatform.url) {
        window.open(rightPlatform.url, '_blank');
    }
}

function showError(message, panel = 'left') {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-screen';
    errorDiv.innerHTML = `
        <div class="error-icon">⚠️</div>
        <div class="welcome-title">تعذر تحميل المنصة</div>
        <div class="welcome-subtitle">${message}</div>
        <button class="action-btn primary" onclick="openPanelInNewTab('${panel}')" style="margin-top: 20px;">
            ↗️ فتح في تبويب جديد
        </button>
    `;
    
    const panelElement = panel === 'left' ? leftPanel : rightPanel;
    const container = panelElement.querySelector('.iframe-container');
    container.innerHTML = '';
    container.appendChild(errorDiv);
}

// تهيئة التطبيق
function initializeApp() {
    // تفعيل وضع المقارنة افتراضياً
    comparisonToggle.classList.add('active');
    contentArea.classList.remove('single-view');
    
    // تفعيل الصفحة الرئيسية افتراضياً
    document.querySelector('[data-platform="welcome"]').classList.add('active');
    
    // إظهار رسالة توضيحية
    showComparisonHint();
}

function showComparisonHint() {
    const hint = document.createElement('div');
    hint.className = 'comparison-mode-hint show';
    hint.textContent = 'وضع المقارنة مفعل - انقر على المنصات لتحميلها في القسمين';
    document.body.appendChild(hint);
    
    setTimeout(() => {
        hint.classList.remove('show');
        setTimeout(() => hint.remove(), 300);
    }, 3000);
}

// تشغيل التهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', initializeApp);

// إدارة نافذة إضافة منصة جديدة
const addPlatformBtn = document.getElementById('addPlatformBtn');
const modal = document.getElementById('addPlatformModal');
const closeModal = document.getElementById('closeModal');
const cancelAdd = document.getElementById('cancelAdd');
const savePlatformBtn = document.getElementById('savePlatform');
const platformNameInput = document.getElementById('platformName');
const platformUrlInput = document.getElementById('platformUrl');
const platformIconInput = document.getElementById('platformIcon');
const platformCategorySelect = document.getElementById('platformCategory');

// فتح النافذة المنبثقة
addPlatformBtn.addEventListener('click', () => {
    modal.style.display = 'flex';
    platformNameInput.focus();
});

// إغلاق النافذة المنبثقة
function closePlatformModal() {
    modal.style.display = 'none';
    // مسح الحقول
    platformNameInput.value = '';
    platformUrlInput.value = '';
    platformIconInput.value = '';
    platformCategorySelect.value = 'chat';
}

// إغلاق عند النقر على زر الإغلاق أو زر الإلغاء
closeModal.addEventListener('click', closePlatformModal);
cancelAdd.addEventListener('click', closePlatformModal);

// إغلاق عند النقر خارج النافذة
window.addEventListener('click', (e) => {
    if (e.target === modal) {
        closePlatformModal();
    }
});

// حفظ المنصة الجديدة
savePlatformBtn.addEventListener('click', () => {
    const name = platformNameInput.value.trim();
    const url = platformUrlInput.value.trim();
    const icon = platformIconInput.value.trim() || '🌐';
    const category = platformCategorySelect.value;

    if (!name || !url) {
        alert('الرجاء إدخال اسم المنصة والرابط');
        return;
    }

    try {
        new URL(url); // التحقق من صحة الرابط
    } catch (error) {
        alert('الرجاء إدخال رابط صحيح: ' + error.message);
        return;
    }

    // إضافة المنصة الجديدة إلى القائمة المناسبة
    const platformGroups = {
        chat: document.querySelector('.platform-group:nth-child(1)'),
        code: document.querySelector('.platform-group:nth-child(2)'),
        tools: document.querySelector('.platform-group:nth-child(3)')
    };

    // إنشاء عنصر المنصة الجديدة
    const platformItem = document.createElement('div');
    platformItem.className = 'platform-item';
    platformItem.setAttribute('data-platform', name.toLowerCase().replace(/\s+/g, '-'));
    platformItem.setAttribute('data-url', url);
    platformItem.setAttribute('data-icon', icon);
    platformItem.innerHTML = `
        <div class="platform-icon">${icon}</div>
        <div class="platform-name">${name}</div>
    `;

    // إضافة حدث النقر
    platformItem.addEventListener('click', () => {
        loadPlatform(url, name, icon);
    });

    // إضافة المنصة إلى المجموعة المناسبة
    const group = platformGroups[category];
    if (group) {
        // البحث عن عنصر group-title التالي وإدراج قبله
        const nextGroup = group.nextElementSibling;
        if (nextGroup?.classList.contains('platform-group')) {
            group.parentNode.insertBefore(platformItem, nextGroup);
        } else {
            group.appendChild(platformItem);
        }
    } else {
        // إذا لم تكن هناك مجموعة محددة، أضف إلى مجموعة المحادثة
        platformGroups.chat.appendChild(platformItem);
    }

    // إغلاق النافذة وإعادة تعيين الحقول
    closePlatformModal();

    // إظهار رسالة نجاح
    alert(`تمت إضافة منصة ${name} بنجاح!`);
});

// منع النقر بالزر الأيمن على الإطار (اختياري)
document.addEventListener('contextmenu', function(e) {
    if (e.target.tagName === 'IFRAME') {
        e.preventDefault();
    }
});
