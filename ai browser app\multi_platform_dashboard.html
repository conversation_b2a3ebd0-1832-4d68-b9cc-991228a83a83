<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منصات الذكاء الاصطناعي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1a1a;
            color: #e1e1e1;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        .sidebar {
            width: 240px;
            background: #1e293b;
            border-left: 1px solid #334155;
            display: flex;
            flex-direction: column;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
        }

        .sidebar-header {
            padding: 24px 16px;
            border-bottom: 1px solid #334155;
            text-align: center;
            background: #1e293b;
            position: relative;
        }

        .sidebar-header h2 {
            font-size: 14px;
            font-weight: 600;
            color: #e2e8f0;
            margin-bottom: 4px;
        }

        .ai-platforms {
            flex: 1;
            padding: 8px 0;
        }

        .platform-group {
            margin-bottom: 16px;
        }

        .group-title {
            padding: 12px 16px 8px;
            font-size: 11px;
            font-weight: 600;
            color: #94a3b8;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: flex;
            align-items: center;
        }
        
        .group-title::before {
            content: '';
            flex: 1;
            height: 1px;
            background: #334155;
            margin-left: 8px;
        }

        .platform-item {
            display: flex;
            align-items: center;
            padding: 10px 16px;
            margin: 2px 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            border-radius: 6px;
            position: relative;
        }

        .platform-item:hover {
            background: #4a5568;
        }

        .platform-item.active {
            background: rgba(59, 130, 246, 0.2);
            color: #60a5fa;
            font-weight: 500;
        }

        .platform-item.active::after {
            content: '';
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: #63b3ed;
        }

        .platform-icon {
            width: 24px;
            height: 24px;
            margin-left: 12px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            background: rgba(255, 255, 255, 0.05);
        }

        .platform-name {
            font-size: 14px;
            font-weight: 400;
            color: #e2e8f0;
        }

        .external-links {
            border-top: 1px solid #334155;
            padding: 8px 0;
            margin-top: auto;
        }
        
        .add-platform-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 12px 16px;
            margin: 8px;
            background: rgba(59, 130, 246, 0.1);
            color: #60a5fa;
            border: 1px dashed #3b82f6;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
            font-weight: 500;
        }
        
        .add-platform-btn:hover {
            background: rgba(59, 130, 246, 0.2);
        }
        
        .add-platform-btn i {
            margin-left: 8px;
            font-size: 16px;
        }
        
        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            z-index: 1000;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(4px);
        }
        
        .modal-content {
            background: #1e293b;
            border-radius: 12px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            border: 1px solid #334155;
            animation: modalFadeIn 0.3s ease-out;
        }
        
        @keyframes modalFadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .modal-header {
            padding: 16px 20px;
            border-bottom: 1px solid #334155;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .modal-title {
            font-size: 16px;
            font-weight: 600;
            color: #f1f5f9;
        }
        
        .close-btn {
            background: none;
            border: none;
            color: #94a3b8;
            font-size: 20px;
            cursor: pointer;
            padding: 4px;
            line-height: 1;
        }
        
        .close-btn:hover {
            color: #e2e8f0;
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-size: 13px;
            color: #94a3b8;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            padding: 10px 12px;
            background: #1e293b;
            border: 1px solid #334155;
            border-radius: 6px;
            color: #e2e8f0;
            font-size: 14px;
            transition: border-color 0.2s;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #60a5fa;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
        }
        
        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            padding: 16px 20px;
            border-top: 1px solid #334155;
            background: #1e293b;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid transparent;
        }
        
        .btn-secondary {
            background: #334155;
            color: #e2e8f0;
        }
        
        .btn-secondary:hover {
            background: #3e4c5f;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2563eb;
        }

        .external-link {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #a0aec0;
            text-decoration: none;
        }

        .external-link:hover {
            background: #4a5568;
            color: #e2e8f0;
        }

        .external-link .platform-icon {
            background: transparent;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #1a1a1a;
        }

        .top-bar {
            height: 60px;
            background: #2d3748;
            border-bottom: 1px solid #4a5568;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            position: relative;
        }

        .comparison-toggle {
            display: flex;
            align-items: center;
            gap: 12px;
            background: #1e293b;
            padding: 8px 16px;
            border-radius: 8px;
            border: 1px solid #334155;
        }

        .toggle-label {
            font-size: 14px;
            color: #e2e8f0;
            font-weight: 500;
        }

        .toggle-switch {
            position: relative;
            width: 50px;
            height: 24px;
            background: #4a5568;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .toggle-switch.active {
            background: #4299e1;
        }

        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }

        .toggle-switch.active .toggle-slider {
            transform: translateX(26px);
        }

        .top-bar-actions {
            position: absolute;
            right: 20px;
            display: flex;
            gap: 8px;
        }

        .action-btn {
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s ease;
            background: #4a5568;
            color: #e2e8f0;
        }

        .action-btn:hover {
            background: #718096;
        }

        .action-btn.primary {
            background: #4299e1;
            color: white;
        }

        .action-btn.primary:hover {
            background: #3182ce;
        }

        .content-area {
            flex: 1;
            display: flex;
            transition: all 0.3s ease;
        }

        .content-area.single-view .platform-panel:not(.active) {
            display: none;
        }

        .platform-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #1a1a1a;
            position: relative;
        }

        .platform-panel + .platform-panel {
            border-right: 2px solid #334155;
        }

        .panel-header {
            height: 50px;
            background: #1e293b;
            border-bottom: 1px solid #334155;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            transition: background-color 0.2s ease;
        }

        .panel-platform-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .panel-platform-icon {
            width: 24px;
            height: 24px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }

        .panel-platform-name {
            font-size: 14px;
            font-weight: 500;
            color: #e2e8f0;
        }

        .panel-actions {
            display: flex;
            gap: 6px;
        }

        .panel-btn {
            padding: 6px 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            font-weight: 500;
            transition: all 0.2s ease;
            background: #334155;
            color: #e2e8f0;
        }

        .panel-btn:hover {
            background: #475569;
        }

        .panel-btn.primary {
            background: #3b82f6;
            color: white;
        }

        .panel-btn.primary:hover {
            background: #2563eb;
        }

        .iframe-container {
            flex: 1;
            position: relative;
            background: #1a1a1a;
        }

        .platform-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }

        .welcome-screen {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-align: center;
            background: #1a1a1a;
        }

        .welcome-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.6;
        }

        .welcome-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #e2e8f0;
        }

        .welcome-subtitle {
            font-size: 14px;
            color: #a0aec0;
            margin-bottom: 24px;
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #a0aec0;
        }

        .loading-spinner {
            width: 32px;
            height: 32px;
            border: 2px solid #4a5568;
            border-top: 2px solid #4299e1;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 12px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-screen {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-align: center;
            background: #1a1a1a;
            color: #e2e8f0;
        }

        .error-icon {
            font-size: 48px;
            margin-bottom: 16px;
            color: #f56565;
        }

        /* Icons colors */
        .chatgpt { background: #10a37f; }
        .gemini { background: #4285f4; }
        .claude { background: #d97706; }
        .deepseek { background: #6366f1; }
        .perplexity { background: #20b2aa; }
        .copilot { background: #0078d4; }
        .meta { background: #1877f2; }
        .huggingface { background: #ff9900; }
        .openai { background: #10a37f; }
        .anthropic { background: #d97706; }

        /* تحسينات إضافية */
        .platform-panel.active {
            box-shadow: inset 0 0 0 2px #3b82f6;
        }

        .platform-panel.active .panel-header {
            background: #1e40af;
        }

        .comparison-mode-hint {
            position: absolute;
            top: 70px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(59, 130, 246, 0.9);
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 100;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .comparison-mode-hint.show {
            opacity: 1;
        }

        /* تحسينات الاستجابة */
        @media (max-width: 1200px) {
            .content-area {
                flex-direction: column;
            }

            .platform-panel + .platform-panel {
                border-right: none;
                border-top: 2px solid #334155;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 180px;
            }

            .platform-name {
                font-size: 12px;
            }

            .comparison-toggle {
                padding: 6px 12px;
            }

            .toggle-label {
                font-size: 12px;
            }

            .top-bar-actions {
                position: static;
                margin-top: 8px;
            }

            .top-bar {
                flex-direction: column;
                height: auto;
                padding: 12px 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2 style="font-size: 18px; color: #f1f5f9; font-weight: 600;">منصات الذكاء</h2>
                <div style="font-size: 12px; color: #94a3b8; margin-top: 4px;">اختر منصة للبدء</div>
            </div>
            
            <div class="ai-platforms">
                <!-- المحادثة -->
                <div class="platform-group">
                    <div class="group-title">المحادثة</div>
                    
                    <div class="platform-item" data-platform="welcome">
                        <div class="platform-icon">🏠</div>
                        <div class="platform-name">الرئيسية</div>
                    </div>
                    
                    <div class="platform-item" data-platform="chatgpt" data-url="https://chatgpt.com">
                        <div class="platform-icon chatgpt">🤖</div>
                        <div class="platform-name">ChatGPT</div>
                    </div>

                    <div class="platform-item" data-platform="gemini" data-url="https://gemini.google.com">
                        <div class="platform-icon gemini">💎</div>
                        <div class="platform-name">Gemini</div>
                    </div>

                    <div class="platform-item" data-platform="claude" data-url="https://claude.ai">
                        <div class="platform-icon claude">🧠</div>
                        <div class="platform-name">Claude</div>
                    </div>

                    <div class="platform-item" data-platform="deepseek" data-url="https://chat.deepseek.com">
                        <div class="platform-icon deepseek">🔍</div>
                        <div class="platform-name">DeepSeek</div>
                    </div>

                    <div class="platform-item" data-platform="perplexity" data-url="https://perplexity.ai">
                        <div class="platform-icon perplexity">🎯</div>
                        <div class="platform-name">Perplexity</div>
                    </div>

                    <!-- منصات تجريبية تعمل مع iframe -->
                    <div class="platform-item" data-platform="demo1" data-url="https://www.google.com/search?q=AI+chatbots&igu=1">
                        <div class="platform-icon">🔍</div>
                        <div class="platform-name">بحث Google</div>
                    </div>

                    <div class="platform-item" data-platform="demo2" data-url="https://www.bing.com/search?q=artificial+intelligence">
                        <div class="platform-icon">🌐</div>
                        <div class="platform-name">بحث Bing</div>
                    </div>
                </div>

                <!-- البرمجة والتطوير -->
                <div class="platform-group">
                    <div class="group-title">البرمجة</div>
                    
                    <div class="platform-item" data-platform="copilot" data-url="https://copilot.microsoft.com">
                        <div class="platform-icon copilot">👨‍💻</div>
                        <div class="platform-name">Copilot</div>
                    </div>
                    
                    <div class="platform-item" data-platform="cursor" data-url="https://cursor.sh">
                        <div class="platform-icon">💻</div>
                        <div class="platform-name">Cursor</div>
                    </div>
                </div>

                <!-- الأدوات المتخصصة -->
                <div class="platform-group">
                    <div class="group-title">أدوات متخصصة</div>
                    
                    <div class="platform-item" data-platform="meta" data-url="https://ai.meta.com">
                        <div class="platform-icon meta">📘</div>
                        <div class="platform-name">Meta AI</div>
                    </div>
                    
                    <div class="platform-item" data-platform="huggingface" data-url="https://huggingface.co/chat">
                        <div class="platform-icon huggingface">🤗</div>
                        <div class="platform-name">HuggingFace</div>
                    </div>
                    
                    <div class="platform-item" data-platform="character" data-url="https://character.ai">
                        <div class="platform-icon">🎭</div>
                        <div class="platform-name">Character.AI</div>
                    </div>
                </div>
            </div>

            <!-- روابط خارجية -->
            <div class="external-links">
                <div class="group-title">روابط مفيدة</div>
                <div class="external-links">
                    <a href="https://openai.com" target="_blank" class="external-link">
                        <div class="platform-icon">🔗</div>
                        <span class="platform-name">OpenAI</span>
                    </a>
                    <a href="https://anthropic.com" target="_blank" class="external-link">
                        <div class="platform-icon">🔗</div>
                        <span class="platform-name">Anthropic</span>
                    </a>
                    <div class="add-platform-btn" id="addPlatformBtn">
                        <span>إضافة منصة جديدة</span>
                        <i>+</i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="top-bar">
                <div class="comparison-toggle">
                    <span class="toggle-label">وضع المقارنة</span>
                    <div class="toggle-switch" id="comparisonToggle">
                        <div class="toggle-slider"></div>
                    </div>
                </div>
                <div class="top-bar-actions">
                    <button class="action-btn" onclick="swapPanels()">🔄 تبديل المواضع</button>
                    <button class="action-btn primary" onclick="openAllInNewTabs()">↗️ فتح الكل خارجياً</button>
                </div>
            </div>

            <div class="content-area" id="contentArea">
                <!-- القسم الأيسر -->
                <div class="platform-panel active" id="leftPanel">
                    <div class="panel-header">
                        <div class="panel-platform-info">
                            <div class="panel-platform-icon" id="leftIcon">🏠</div>
                            <div class="panel-platform-name" id="leftPlatform">اختر منصة</div>
                        </div>
                        <div class="panel-actions">
                            <button class="panel-btn" onclick="refreshPanel('left')">🔄</button>
                            <button class="panel-btn primary" onclick="openPanelInNewTab('left')">↗️</button>
                        </div>
                    </div>

                    <div class="iframe-container">
                        <div class="welcome-screen" id="leftWelcomeScreen">
                            <div class="welcome-icon">🤖</div>
                            <div class="welcome-title">القسم الأيسر</div>
                            <div class="welcome-subtitle">اختر منصة من الشريط الجانبي</div>
                            <div style="margin-top: 20px; color: #718096; font-size: 12px;">
                                💡 نصيحة: فعّل وضع المقارنة لاستخدام قسمين جانبيين
                            </div>
                        </div>

                        <div class="loading" id="leftLoadingIndicator" style="display: none;">
                            <div class="loading-spinner"></div>
                            <p>جاري تحميل المنصة...</p>
                        </div>

                        <iframe class="platform-iframe" id="leftFrame" style="display: none;" title="Left Platform Frame"></iframe>
                    </div>
                </div>

                <!-- القسم الأيمن -->
                <div class="platform-panel" id="rightPanel">
                    <div class="panel-header">
                        <div class="panel-platform-info">
                            <div class="panel-platform-icon" id="rightIcon">🏠</div>
                            <div class="panel-platform-name" id="rightPlatform">اختر منصة</div>
                        </div>
                        <div class="panel-actions">
                            <button class="panel-btn" onclick="refreshPanel('right')">🔄</button>
                            <button class="panel-btn primary" onclick="openPanelInNewTab('right')">↗️</button>
                        </div>
                    </div>

                    <div class="iframe-container">
                        <div class="welcome-screen" id="rightWelcomeScreen">
                            <div class="welcome-icon">🤖</div>
                            <div class="welcome-title">القسم الأيمن</div>
                            <div class="welcome-subtitle">اختر منصة من الشريط الجانبي</div>
                            <div style="margin-top: 20px; color: #718096; font-size: 12px;">
                                💡 نصيحة: انقر مع الضغط على Ctrl لتحديد هذا القسم
                            </div>
                        </div>

                        <div class="loading" id="rightLoadingIndicator" style="display: none;">
                            <div class="loading-spinner"></div>
                            <p>جاري تحميل المنصة...</p>
                        </div>

                        <iframe class="platform-iframe" id="rightFrame" style="display: none;" title="Right Platform Frame"></iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal إضافة منصة جديدة -->
    <div id="addPlatformModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <span class="modal-title">إضافة منصة جديدة</span>
                <button class="close-btn" id="closeModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="platformName">اسم المنصة</label>
                    <input type="text" id="platformName" class="form-control" placeholder="مثال: منصتي الجديدة">
                </div>
                <div class="form-group">
                    <label for="platformUrl">رابط المنصة</label>
                    <input type="url" id="platformUrl" class="form-control" placeholder="https://example.com">
                </div>
                <div class="form-group">
                    <label for="platformIcon">أيقونة المنصة (اختياري)</label>
                    <input type="text" id="platformIcon" class="form-control" placeholder="مثال: 🤖">
                </div>
                <div class="form-group">
                    <label for="platformCategory">التصنيف</label>
                    <select id="platformCategory" class="form-control">
                        <option value="chat">💬 المحادثة</option>
                        <option value="code">👨‍💻 البرمجة</option>
                        <option value="tools">🔧 أدوات متخصصة</option>
                    </select>
                </div>
            </div>
            <div class="form-actions">
                <button class="btn btn-secondary" id="cancelAdd">إلغاء</button>
                <button class="btn btn-primary" id="savePlatform">حفظ المنصة</button>
            </div>
        </div>
    </div>

    <script>
        // متغيرات النظام
        let isComparisonMode = true; // تفعيل وضع المقارنة افتراضياً
        let activePanel = 'left';
        let leftPlatform = { url: '', name: '', icon: '' };
        let rightPlatform = { url: '', name: '', icon: '' };

        // عناصر DOM
        const platformItems = document.querySelectorAll('.platform-item');
        const comparisonToggle = document.getElementById('comparisonToggle');
        const contentArea = document.getElementById('contentArea');
        const leftPanel = document.getElementById('leftPanel');
        const rightPanel = document.getElementById('rightPanel');

        // عناصر القسم الأيسر
        const leftFrame = document.getElementById('leftFrame');
        const leftWelcomeScreen = document.getElementById('leftWelcomeScreen');
        const leftLoadingIndicator = document.getElementById('leftLoadingIndicator');
        const leftPlatformEl = document.getElementById('leftPlatform');
        const leftIconEl = document.getElementById('leftIcon');

        // عناصر القسم الأيمن
        const rightFrame = document.getElementById('rightFrame');
        const rightWelcomeScreen = document.getElementById('rightWelcomeScreen');
        const rightLoadingIndicator = document.getElementById('rightLoadingIndicator');
        const rightPlatformEl = document.getElementById('rightPlatform');
        const rightIconEl = document.getElementById('rightIcon');

        // تفعيل/إلغاء وضع المقارنة
        comparisonToggle.addEventListener('click', function() {
            isComparisonMode = !isComparisonMode;
            this.classList.toggle('active', isComparisonMode);
            contentArea.classList.toggle('single-view', !isComparisonMode);

            if (isComparisonMode) {
                leftPanel.classList.add('active');
                rightPanel.classList.remove('active');
                activePanel = 'left';
            } else {
                leftPanel.classList.add('active');
                rightPanel.classList.remove('active');
            }
        });

        // إضافة مستمعي الأحداث للمنصات
        platformItems.forEach(item => {
            item.addEventListener('click', function(event) {
                const platform = this.dataset.platform;
                const url = this.dataset.url;
                const name = this.querySelector('.platform-name').textContent;
                const iconEl = this.querySelector('.platform-icon');
                const icon = iconEl.textContent;

                // تحديد القسم المستهدف
                let targetPanel = activePanel;
                if (isComparisonMode && event.ctrlKey) {
                    targetPanel = activePanel === 'left' ? 'right' : 'left';
                }

                if (platform === 'welcome') {
                    showWelcomeScreen(targetPanel);
                } else {
                    loadPlatform(url, name, icon, targetPanel);
                }

                // تحديث الحالة النشطة
                platformItems.forEach(i => i.classList.remove('active'));
                this.classList.add('active');

                // تحديث القسم النشط
                if (isComparisonMode) {
                    leftPanel.classList.toggle('active', targetPanel === 'left');
                    rightPanel.classList.toggle('active', targetPanel === 'right');
                    activePanel = targetPanel;
                }
            });
        });

        function loadPlatform(url, name, icon, panel = 'left') {
            const platformData = { url, name, icon };

            if (panel === 'left') {
                leftPlatform = platformData;
                loadPlatformInPanel(url, name, icon, 'left');
            } else {
                rightPlatform = platformData;
                loadPlatformInPanel(url, name, icon, 'right');
            }
        }

        function loadPlatformInPanel(url, name, icon, panel) {
            const frame = panel === 'left' ? leftFrame : rightFrame;
            const welcomeScreen = panel === 'left' ? leftWelcomeScreen : rightWelcomeScreen;
            const loadingIndicator = panel === 'left' ? leftLoadingIndicator : rightLoadingIndicator;
            const platformEl = panel === 'left' ? leftPlatformEl : rightPlatformEl;
            const iconEl = panel === 'left' ? leftIconEl : rightIconEl;

            // إخفاء الشاشة الترحيبية وإظهار مؤشر التحميل
            welcomeScreen.style.display = 'none';
            loadingIndicator.style.display = 'block';
            frame.style.display = 'none';

            // تحديث عنوان المنصة والأيقونة
            platformEl.textContent = name;
            iconEl.textContent = icon;

            // إضافة معالج للأخطاء
            frame.onerror = function() {
                loadingIndicator.style.display = 'none';
                showError('لا يمكن تحميل هذه المنصة في الإطار. استخدم زر "فتح خارجياً" بدلاً من ذلك.', panel);
            };

            // تحميل المنصة
            frame.src = url;

            // إظهار الإطار بعد التحميل
            frame.onload = function() {
                try {
                    // محاولة الوصول للمحتوى للتأكد من التحميل الناجح
                    if (frame.contentDocument || frame.contentWindow) {
                        loadingIndicator.style.display = 'none';
                        frame.style.display = 'block';
                    } else {
                        throw new Error('Access denied');
                    }
                } catch (e) {
                    loadingIndicator.style.display = 'none';
                    showError('هذه المنصة لا تدعم التحميل المدمج. استخدم زر "فتح خارجياً".', panel);
                }
            };

            // في حالة فشل التحميل أو طول مدة التحميل
            setTimeout(() => {
                if (loadingIndicator.style.display !== 'none') {
                    loadingIndicator.style.display = 'none';
                    showError('انتهت مهلة التحميل. جرب فتح المنصة في تبويب منفصل.', panel);
                }
            }, 8000);
        }

        function showWelcomeScreen(panel = 'left') {
            const frame = panel === 'left' ? leftFrame : rightFrame;
            const welcomeScreen = panel === 'left' ? leftWelcomeScreen : rightWelcomeScreen;
            const loadingIndicator = panel === 'left' ? leftLoadingIndicator : rightLoadingIndicator;
            const platformEl = panel === 'left' ? leftPlatformEl : rightPlatformEl;
            const iconEl = panel === 'left' ? leftIconEl : rightIconEl;

            frame.style.display = 'none';
            loadingIndicator.style.display = 'none';
            welcomeScreen.style.display = 'flex';
            platformEl.textContent = 'اختر منصة';
            iconEl.textContent = '🏠';

            if (panel === 'left') {
                leftPlatform = { url: '', name: '', icon: '' };
            } else {
                rightPlatform = { url: '', name: '', icon: '' };
            }
        }

        function refreshPanel(panel) {
            const platformData = panel === 'left' ? leftPlatform : rightPlatform;
            if (platformData.url) {
                loadPlatformInPanel(platformData.url, platformData.name, platformData.icon, panel);
            }
        }

        function openPanelInNewTab(panel) {
            const platformData = panel === 'left' ? leftPlatform : rightPlatform;
            if (platformData.url) {
                window.open(platformData.url, '_blank');
            }
        }

        function swapPanels() {
            if (isComparisonMode && leftPlatform.url && rightPlatform.url) {
                const temp = leftPlatform;
                leftPlatform = rightPlatform;
                rightPlatform = temp;

                loadPlatformInPanel(leftPlatform.url, leftPlatform.name, leftPlatform.icon, 'left');
                loadPlatformInPanel(rightPlatform.url, rightPlatform.name, rightPlatform.icon, 'right');
            }
        }

        function openAllInNewTabs() {
            if (leftPlatform.url) {
                window.open(leftPlatform.url, '_blank');
            }
            if (rightPlatform.url) {
                window.open(rightPlatform.url, '_blank');
            }
        }

        function showError(message, panel = 'left') {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-screen';
            errorDiv.innerHTML = `
                <div class="error-icon">⚠️</div>
                <div class="welcome-title">تعذر تحميل المنصة</div>
                <div class="welcome-subtitle">${message}</div>
                <button class="action-btn primary" onclick="openPanelInNewTab('${panel}')" style="margin-top: 20px;">
                    ↗️ فتح في تبويب جديد
                </button>
            `;

            const panelElement = panel === 'left' ? leftPanel : rightPanel;
            const container = panelElement.querySelector('.iframe-container');
            container.innerHTML = '';
            container.appendChild(errorDiv);
        }

        // تهيئة التطبيق
        function initializeApp() {
            // تفعيل وضع المقارنة افتراضياً
            comparisonToggle.classList.add('active');
            contentArea.classList.remove('single-view');

            // تفعيل الصفحة الرئيسية افتراضياً
            document.querySelector('[data-platform="welcome"]').classList.add('active');

            // إظهار رسالة توضيحية
            showComparisonHint();
        }

        function showComparisonHint() {
            const hint = document.createElement('div');
            hint.className = 'comparison-mode-hint show';
            hint.textContent = 'وضع المقارنة مفعل - انقر على المنصات لتحميلها في القسمين';
            document.body.appendChild(hint);

            setTimeout(() => {
                hint.classList.remove('show');
                setTimeout(() => hint.remove(), 300);
            }, 3000);
        }

        // تشغيل التهيئة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', initializeApp);
        
        // إدارة نافذة إضافة منصة جديدة
        const addPlatformBtn = document.getElementById('addPlatformBtn');
        const modal = document.getElementById('addPlatformModal');
        const closeModal = document.getElementById('closeModal');
        const cancelAdd = document.getElementById('cancelAdd');
        const savePlatformBtn = document.getElementById('savePlatform');
        const platformNameInput = document.getElementById('platformName');
        const platformUrlInput = document.getElementById('platformUrl');
        const platformIconInput = document.getElementById('platformIcon');
        const platformCategorySelect = document.getElementById('platformCategory');
        
        // فتح النافذة المنبثقة
        addPlatformBtn.addEventListener('click', () => {
            modal.style.display = 'flex';
            platformNameInput.focus();
        });
        
        // إغلاق النافذة المنبثقة
        function closePlatformModal() {
            modal.style.display = 'none';
            // مسح الحقول
            platformNameInput.value = '';
            platformUrlInput.value = '';
            platformIconInput.value = '';
            platformCategorySelect.value = 'chat';
        }
        
        // إغلاق عند النقر على زر الإغلاق أو زر الإلغاء
        closeModal.addEventListener('click', closePlatformModal);
        cancelAdd.addEventListener('click', closePlatformModal);
        
        // إغلاعند النقر خارج النافذة
        window.addEventListener('click', (e) => {
            if (e.target === modal) {
                closePlatformModal();
            }
        });
        
        // حفظ المنصة الجديدة
        savePlatformBtn.addEventListener('click', () => {
            const name = platformNameInput.value.trim();
            const url = platformUrlInput.value.trim();
            const icon = platformIconInput.value.trim() || '🌐';
            const category = platformCategorySelect.value;
            
            if (!name || !url) {
                alert('الرجاء إدخال اسم المنصة والرابط');
                return;
            }
            
            try {
                new URL(url); // التحقق من صحة الرابط
            } catch (error) {
                alert('الرجاء إدخال رابط صحيح: ' + error.message);
                return;
            }
            
            // إضافة المنصة الجديدة إلى القائمة المناسبة
            const platformGroups = {
                chat: document.querySelector('.platform-group:nth-child(1)'),
                code: document.querySelector('.platform-group:nth-child(2)'),
                tools: document.querySelector('.platform-group:nth-child(3)')
            };
            
            // إنشاء عنصر المنصة الجديدة
            const platformItem = document.createElement('div');
            platformItem.className = 'platform-item';
            platformItem.setAttribute('data-platform', name.toLowerCase().replace(/\s+/g, '-'));
            platformItem.setAttribute('data-url', url);
            platformItem.setAttribute('data-icon', icon);
            platformItem.innerHTML = `
                <div class="platform-icon">${icon}</div>
                <div class="platform-name">${name}</div>
            `;
            
            // إضافة حدث النقر
            platformItem.addEventListener('click', () => {
                loadPlatform(url, name, icon);
            });
            
            // إضافة المنصة إلى المجموعة المناسبة
            const group = platformGroups[category];
            if (group) {
                // البحث عن عنصر group-title التالي وإدراج قبله
                const nextGroup = group.nextElementSibling;
                if (nextGroup && nextGroup.classList.contains('platform-group')) {
                    group.parentNode.insertBefore(platformItem, nextGroup);
                } else {
                    group.appendChild(platformItem);
                }
            } else {
                // إذا لم تكن هناك مجموعة محددة، أضف إلى مجموعة المحادثة
                platformGroups.chat.appendChild(platformItem);
            }
            
            // إغلاق النافذة وإعادة تعيين الحقول
            closePlatformModal();
            
            // إظهار رسالة نجاح
            alert(`تمت إضافة منصة ${name} بنجاح!`);
        });

        // منع النقر بالزر الأيمن على الإطار (اختياري)
        document.addEventListener('contextmenu', function(e) {
            if (e.target.tagName === 'IFRAME') {
                e.preventDefault();
            }
        });
    </script>
</body>
</html>